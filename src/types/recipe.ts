export interface Ingredient {
  name: string;
  amount: string;
  notes?: string;
}

export interface Instruction {
  step: number;
  instruction: string;
  time?: string;
  temperature?: string;
}

export interface Recipe {
  name: string;
  description: string;
  cuisine?: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  prepTime: string;
  cookTime: string;
  totalTime: string;
  servings: number;
  ingredients: Ingredient[];
  instructions: Instruction[];
  tips?: string[];
  tags?: string[];
}

export interface Nutrition {
  calories: number;
  protein: string;
  carbs: string;
  fat: string;
  fiber?: string;
  sugar?: string;
}

export interface Substitution {
  original: string;
  substitute: string;
  ratio: string;
  notes?: string;
}

export interface RecipeToolResult {
  success: boolean;
  recipe?: Recipe;
  nutrition?: Nutrition;
  substitutions?: Substitution[];
  suggestions?: string[];
  ratio?: number;
  instructions?: string[];
  message: string;
  disclaimer?: string;
}
