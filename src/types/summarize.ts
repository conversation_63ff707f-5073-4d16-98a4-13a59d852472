export interface SummaryRequest {
  content: string;
  maxWords?: number;
  type?: 'text' | 'file';
  fileName?: string;
}

export interface SummaryResponse {
  summary: string;
  wordCount: number;
  originalWordCount: number;
  compressionRatio: number;
  processingTime: number;
}

export interface FileUpload {
  file: File;
  type: string;
  size: number;
  name: string;
}

export interface SummarySettings {
  maxWords: number;
  style: 'concise' | 'detailed' | 'bullet-points';
  focus: 'general' | 'key-points' | 'action-items';
}
