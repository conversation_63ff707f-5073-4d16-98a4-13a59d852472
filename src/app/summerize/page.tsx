"use client";
import { useChat } from "@ai-sdk/react";
import { useState, useRef } from "react";

export default function SummarizePage() {
  const { messages, input, handleInputChange, handleSubmit, status } = useChat({
    api: "/api/summarize",
  });

  const [maxWords, setMaxWords] = useState(120);
  const [summaryStyle, setSummaryStyle] = useState<'concise' | 'detailed' | 'bullet-points'>('concise');
  const [summaryFocus, setSummaryFocus] = useState<'general' | 'key-points' | 'action-items'>('general');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  const handleFileUpload = async (file: File) => {
    const allowedTypes = ['text/plain', 'application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    if (!allowedTypes.includes(file.type)) {
      alert('Please upload a TXT, PDF, or DOCX file.');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      alert('File size must be less than 10MB.');
      return;
    }

    try {
      let content = '';

      if (file.type === 'text/plain') {
        content = await file.text();
      } else {
        // For now, show a message that PDF/DOCX processing will be added
        alert('PDF and DOCX file processing will be available soon. Please use text files for now.');
        return;
      }

      // Create a summarization request
      const summaryRequest = `Please summarize the following content in ${maxWords} words using a ${summaryStyle} style with ${summaryFocus} focus:\n\n${content}`;

      // Trigger the chat with the file content
      const syntheticEvent = {
        preventDefault: () => {},
        target: { value: summaryRequest }
      } as any;

      handleInputChange(syntheticEvent);

      // Auto-submit after a brief delay
      setTimeout(() => {
        handleSubmit(syntheticEvent);
      }, 100);

    } catch (error) {
      console.error('Error processing file:', error);
      alert('Error processing file. Please try again.');
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0]);
    }
  };

  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!input.trim()) {
      alert('Please enter some content to summarize.');
      return;
    }

    // Add summarization parameters to the input
    const enhancedInput = `Please summarize the following content in ${maxWords} words using a ${summaryStyle} style with ${summaryFocus} focus:\n\n${input}`;

    const syntheticEvent = {
      preventDefault: () => {},
      target: { value: enhancedInput }
    } as any;

    handleInputChange(syntheticEvent);
    handleSubmit(syntheticEvent);
  };

  const wordCount = input.trim().split(/\s+/).filter(word => word.length > 0).length;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-6">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            📄 AI Content Summarizer
          </h1>
          <p className="text-gray-600">
            Your specialized summarization assistant! Paste text or upload documents (TXT, PDF, DOCX)
            and get concise summaries with customizable word limits and styles.
          </p>
        </header>

        {/* Settings Panel */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Summary Settings</h3>

          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Words: {maxWords}
              </label>
              <input
                type="range"
                min="50"
                max="300"
                value={maxWords}
                onChange={(e) => setMaxWords(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>50</span>
                <span>300</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Summary Style
              </label>
              <select
                value={summaryStyle}
                onChange={(e) => setSummaryStyle(e.target.value as any)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="concise">Concise</option>
                <option value="detailed">Detailed</option>
                <option value="bullet-points">Bullet Points</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Focus Area
              </label>
              <select
                value={summaryFocus}
                onChange={(e) => setSummaryFocus(e.target.value as any)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="general">General</option>
                <option value="key-points">Key Points</option>
                <option value="action-items">Action Items</option>
              </select>
            </div>
          </div>
        </div>
