"use client";
import { useChat } from "@ai-sdk/react";

export default function RecipeGeneratorPage() {
  const { messages, input, handleInputChange, handleSubmit, status } = useChat({
    api: "/api/recipe",
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-6">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            🍳 AI Recipe Generator
          </h1>
          <p className="text-gray-600">
            Your specialized recipe assistant! Tell me what food you'd like to
            cook, and I'll create a delicious recipe for you. I only help with
            cooking and recipe-related questions.
          </p>
        </header>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <form onSubmit={handleSubmit} className="flex gap-3">
              <input
                value={input}
                type="text"
                className="flex-1 border border-gray-300 text-black rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onChange={handleInputChange}
                placeholder="Enter a food name or recipe request (e.g., 'pasta carbonara', 'chocolate cake', 'chicken curry')..."
                disabled={status === "streaming" || status === "submitted"}
              />
              <button
                type="submit"
                disabled={status === "streaming" || status === "submitted"}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                {status === "streaming" || status === "submitted"
                  ? "Generating..."
                  : "Generate Recipe"}
              </button>
            </form>
          </div>
        </div>

        <div className="space-y-6">
          {/* Chat messages */}
          <div className="space-y-4">
            {messages.map((message, index) => (
              <div
                key={index}
                className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm"
              >
                <div className="flex items-start gap-4">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                      message.role === "user" ? "bg-blue-600" : "bg-green-600"
                    }`}
                  >
                    {message.role === "user" ? "U" : "👨‍🍳"}
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-gray-900 mb-2">
                      {message.role === "user" ? "You" : "AI Chef"}
                    </div>
                    {message.content && (
                      <div className="text-gray-700 prose prose-sm max-w-none">
                        <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                          {message.content}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {messages.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">👨‍🍳</div>
              <h2 className="text-xl font-semibold text-gray-700 mb-2">
                Ready to Cook Something Delicious?
              </h2>
              <p className="text-gray-500">
                Enter the name of any dish above and I'll create a complete
                recipe for you! I only respond to cooking and recipe questions.
              </p>
              <div className="mt-6 flex flex-wrap justify-center gap-2">
                {[
                  "Spaghetti Carbonara",
                  "Chocolate Chip Cookies",
                  "Chicken Tikka Masala",
                  "Caesar Salad",
                ].map((example) => (
                  <button
                    key={example}
                    onClick={() =>
                      handleInputChange({ target: { value: example } } as any)
                    }
                    className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm transition-colors"
                  >
                    {example}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
