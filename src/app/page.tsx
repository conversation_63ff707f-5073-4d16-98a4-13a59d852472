"use client";
import { useChat } from "@ai-sdk/react";
import Link from "next/link";

export default function Home() {
  const { messages, input, handleInputChange, handleSubmit, status } = useChat({
    api: "/api/recipe",
  });

  return (
    <div className="min-h-screen bg-gray-50 ">
      <h1 className="text-4xl font-bold text-gray-900 mb-2">AI Tools</h1>
      <div className="max-w-4xl mx-auto p-6 flex flex-col gap-4">
        <Link href="/recipe">Recipe</Link>
        <Link href="/summerize">Summerize</Link>
      </div>
    </div>
  );
}
