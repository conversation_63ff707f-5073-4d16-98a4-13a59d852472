import { Recipe, Nutrition, Substitution } from "@/types/recipe";

interface RecipeDisplayProps {
  recipe?: Recipe;
  nutrition?: Nutrition;
  substitutions?: Substitution[];
  suggestions?: string[];
}

export function RecipeDisplay({
  recipe,
  nutrition,
  substitutions,
  suggestions,
}: RecipeDisplayProps) {
  if (!recipe && !nutrition && !substitutions && !suggestions) {
    return null;
  }

  return (
    <div className="bg-gradient-to-br from-white via-gray-50 to-white border border-gray-200/60 rounded-2xl p-8 shadow-xl shadow-gray-200/50 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl hover:shadow-gray-300/30">
      {recipe && (
        <div className="mb-8">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <h2 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent mb-2 font-[family-name:var(--font-geist-sans)]">
                {recipe.name}
              </h2>
              {recipe.cuisine && (
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                  {recipe.cuisine} Cuisine
                </p>
              )}
            </div>
            <div className="flex flex-col items-end gap-2">
              <span
                className={`px-4 py-2 rounded-full text-sm font-semibold shadow-lg transition-all duration-200 hover:scale-105 ${
                  recipe.difficulty === "Easy"
                    ? "bg-gradient-to-r from-emerald-400 to-green-500 text-white shadow-emerald-200"
                    : recipe.difficulty === "Medium"
                    ? "bg-gradient-to-r from-amber-400 to-orange-500 text-white shadow-amber-200"
                    : "bg-gradient-to-r from-red-400 to-rose-500 text-white shadow-red-200"
                }`}
              >
                {recipe.difficulty}
              </span>
            </div>
          </div>

          <p className="text-gray-700 mb-6 text-lg leading-relaxed font-[family-name:var(--font-geist-sans)]">
            {recipe.description}
          </p>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100 p-4 rounded-xl transition-all duration-200 hover:shadow-md hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-blue-600">⏱️</span>
                <span className="font-medium text-gray-700 text-sm">
                  Prep Time
                </span>
              </div>
              <div className="text-gray-900 font-bold text-lg">
                {recipe.prepTime}
              </div>
            </div>
            <div className="bg-gradient-to-br from-orange-50 to-red-50 border border-orange-100 p-4 rounded-xl transition-all duration-200 hover:shadow-md hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-orange-600">🔥</span>
                <span className="font-medium text-gray-700 text-sm">
                  Cook Time
                </span>
              </div>
              <div className="text-gray-900 font-bold text-lg">
                {recipe.cookTime}
              </div>
            </div>
            <div className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100 p-4 rounded-xl transition-all duration-200 hover:shadow-md hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-purple-600">⏰</span>
                <span className="font-medium text-gray-700 text-sm">
                  Total Time
                </span>
              </div>
              <div className="text-gray-900 font-bold text-lg">
                {recipe.totalTime}
              </div>
            </div>
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-100 p-4 rounded-xl transition-all duration-200 hover:shadow-md hover:scale-105">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-green-600">👥</span>
                <span className="font-medium text-gray-700 text-sm">
                  Servings
                </span>
              </div>
              <div className="text-gray-900 font-bold text-lg">
                {recipe.servings}
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-gradient-to-br from-emerald-50/50 to-green-50/50 border border-emerald-100 rounded-2xl p-6">
              <div className="flex items-center gap-3 mb-5">
                <div className="bg-gradient-to-r from-emerald-500 to-green-600 p-2 rounded-lg">
                  <span className="text-white text-lg">🥬</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 font-[family-name:var(--font-geist-sans)]">
                  Ingredients
                </h3>
              </div>
              <ul className="space-y-3">
                {recipe.ingredients.map((ingredient, index) => (
                  <li
                    key={index}
                    className="group flex items-start p-3 rounded-xl bg-white/60 border border-emerald-100/50 transition-all duration-200 hover:bg-white hover:shadow-md hover:border-emerald-200"
                  >
                    <div className="bg-gradient-to-r from-emerald-400 to-green-500 w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 group-hover:scale-125 transition-transform duration-200"></div>
                    <div className="flex-1">
                      <div className="flex flex-wrap items-baseline gap-2">
                        <span className="font-bold text-emerald-700 text-lg">
                          {ingredient.amount}
                        </span>
                        <span className="text-gray-900 font-medium">
                          {ingredient.name}
                        </span>
                      </div>
                      {ingredient.notes && (
                        <span className="text-gray-600 text-sm italic mt-1 block">
                          {ingredient.notes}
                        </span>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-gradient-to-br from-blue-50/50 to-indigo-50/50 border border-blue-100 rounded-2xl p-6">
              <div className="flex items-center gap-3 mb-5">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-lg">
                  <span className="text-white text-lg">👨‍🍳</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 font-[family-name:var(--font-geist-sans)]">
                  Instructions
                </h3>
              </div>
              <ol className="space-y-4">
                {recipe.instructions.map((instruction, index) => (
                  <li
                    key={index}
                    className="group flex items-start p-4 rounded-xl bg-white/60 border border-blue-100/50 transition-all duration-200 hover:bg-white hover:shadow-md hover:border-blue-200"
                  >
                    <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5 flex-shrink-0 shadow-lg group-hover:scale-110 transition-transform duration-200">
                      {instruction.step}
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-900 leading-relaxed font-[family-name:var(--font-geist-sans)]">
                        {instruction.instruction}
                      </p>
                      {(instruction.time || instruction.temperature) && (
                        <div className="flex flex-wrap gap-3 mt-3">
                          {instruction.time && (
                            <span className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                              ⏱️ {instruction.time}
                            </span>
                          )}
                          {instruction.temperature && (
                            <span className="inline-flex items-center gap-1 px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm font-medium">
                              🌡️ {instruction.temperature}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </li>
                ))}
              </ol>
            </div>
          </div>

          {recipe.tips && recipe.tips.length > 0 && (
            <div className="mt-8">
              <div className="bg-gradient-to-br from-amber-50/50 to-yellow-50/50 border border-amber-100 rounded-2xl p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-gradient-to-r from-amber-500 to-yellow-600 p-2 rounded-lg">
                    <span className="text-white text-lg">💡</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 font-[family-name:var(--font-geist-sans)]">
                    Pro Tips
                  </h3>
                </div>
                <ul className="space-y-3">
                  {recipe.tips.map((tip, index) => (
                    <li
                      key={index}
                      className="flex items-start p-3 rounded-xl bg-white/60 border border-amber-100/50 transition-all duration-200 hover:bg-white hover:shadow-md"
                    >
                      <div className="bg-gradient-to-r from-amber-400 to-yellow-500 w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-800 leading-relaxed font-[family-name:var(--font-geist-sans)]">
                        {tip}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {recipe.tags && recipe.tags.length > 0 && (
            <div className="mt-6">
              <div className="flex flex-wrap gap-3">
                {recipe.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 rounded-full text-sm font-medium border border-indigo-200 transition-all duration-200 hover:shadow-md hover:scale-105"
                  >
                    <span className="mr-1">🏷️</span>
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {nutrition && (
        <div className="mb-8">
          <div className="bg-gradient-to-br from-teal-50/50 to-cyan-50/50 border border-teal-100 rounded-2xl p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="bg-gradient-to-r from-teal-500 to-cyan-600 p-2 rounded-lg">
                <span className="text-white text-lg">📊</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 font-[family-name:var(--font-geist-sans)]">
                Nutrition Facts
                <span className="text-sm font-normal text-gray-600 ml-2">
                  (per serving)
                </span>
              </h3>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-gradient-to-br from-red-50 to-pink-50 border border-red-100 p-4 rounded-xl text-center transition-all duration-200 hover:shadow-md hover:scale-105">
                <div className="text-3xl font-bold text-red-600 mb-1">
                  {nutrition.calories}
                </div>
                <div className="text-sm font-medium text-gray-700">
                  Calories
                </div>
              </div>
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100 p-4 rounded-xl text-center transition-all duration-200 hover:shadow-md hover:scale-105">
                <div className="text-2xl font-bold text-blue-600 mb-1">
                  {nutrition.protein}
                </div>
                <div className="text-sm font-medium text-gray-700">Protein</div>
              </div>
              <div className="bg-gradient-to-br from-orange-50 to-amber-50 border border-orange-100 p-4 rounded-xl text-center transition-all duration-200 hover:shadow-md hover:scale-105">
                <div className="text-2xl font-bold text-orange-600 mb-1">
                  {nutrition.carbs}
                </div>
                <div className="text-sm font-medium text-gray-700">Carbs</div>
              </div>
              <div className="bg-gradient-to-br from-yellow-50 to-amber-50 border border-yellow-100 p-4 rounded-xl text-center transition-all duration-200 hover:shadow-md hover:scale-105">
                <div className="text-2xl font-bold text-yellow-600 mb-1">
                  {nutrition.fat}
                </div>
                <div className="text-sm font-medium text-gray-700">Fat</div>
              </div>
              {nutrition.fiber && (
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-100 p-4 rounded-xl text-center transition-all duration-200 hover:shadow-md hover:scale-105">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {nutrition.fiber}
                  </div>
                  <div className="text-sm font-medium text-gray-700">Fiber</div>
                </div>
              )}
              {nutrition.sugar && (
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100 p-4 rounded-xl text-center transition-all duration-200 hover:shadow-md hover:scale-105">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {nutrition.sugar}
                  </div>
                  <div className="text-sm font-medium text-gray-700">Sugar</div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {substitutions && substitutions.length > 0 && (
        <div className="mb-8">
          <div className="bg-gradient-to-br from-orange-50/50 to-amber-50/50 border border-orange-100 rounded-2xl p-6">
            <div className="flex items-center gap-3 mb-5">
              <div className="bg-gradient-to-r from-orange-500 to-amber-600 p-2 rounded-lg">
                <span className="text-white text-lg">🔄</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 font-[family-name:var(--font-geist-sans)]">
                Ingredient Substitutions
              </h3>
            </div>
            <div className="space-y-4">
              {substitutions.map((sub, index) => (
                <div
                  key={index}
                  className="bg-white/60 border border-orange-100/50 rounded-xl p-4 transition-all duration-200 hover:bg-white hover:shadow-md hover:border-orange-200"
                >
                  <div className="flex items-center gap-3 mb-2">
                    <span className="font-bold text-gray-900 text-lg">
                      {sub.original}
                    </span>
                    <div className="bg-gradient-to-r from-orange-400 to-amber-500 text-white px-2 py-1 rounded-full text-sm font-medium">
                      →
                    </div>
                    <span className="font-bold text-orange-700 text-lg">
                      {sub.substitute}
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-3 text-sm">
                    <span className="inline-flex items-center gap-1 px-3 py-1 bg-orange-100 text-orange-700 rounded-full font-medium">
                      📏 Ratio: {sub.ratio}
                    </span>
                    {sub.notes && (
                      <span className="text-gray-600 italic">{sub.notes}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {suggestions && suggestions.length > 0 && (
        <div className="mb-8">
          <div className="bg-gradient-to-br from-violet-50/50 to-purple-50/50 border border-violet-100 rounded-2xl p-6">
            <div className="flex items-center gap-3 mb-5">
              <div className="bg-gradient-to-r from-violet-500 to-purple-600 p-2 rounded-lg">
                <span className="text-white text-lg">✨</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 font-[family-name:var(--font-geist-sans)]">
                Similar Recipes
              </h3>
            </div>
            <div className="grid gap-3">
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="group bg-white/60 border border-violet-100/50 rounded-xl p-4 transition-all duration-200 hover:bg-white hover:shadow-md hover:border-violet-200 cursor-pointer"
                >
                  <div className="flex items-center gap-3">
                    <div className="bg-gradient-to-r from-violet-400 to-purple-500 w-2 h-2 rounded-full group-hover:scale-125 transition-transform duration-200"></div>
                    <span className="text-violet-800 font-semibold text-lg group-hover:text-violet-900 transition-colors duration-200">
                      {suggestion}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
