import { z } from 'zod';
import { tool } from 'ai';

interface SubstitutionSchema {
  original?: string;
  substitute?: string;
  ratio?: string;
  notes?: string;
}

interface NutritionSchema {
  calories?: number;
  protein?: string;
  carbs?: string;
  fat?: string;
  fiber?: string;
  sugar?: string;
}

// Zod schemas for recipe data structures
const IngredientSchema = z.object({
  name: z.string().describe('Name of the ingredient'),
  amount: z.string().describe('Amount/quantity (e.g., "2 cups", "1 tbsp")'),
  notes: z.string().optional().describe('Optional notes about the ingredient'),
});

const InstructionSchema = z.object({
  step: z.number().describe('Step number'),
  instruction: z.string().describe('Detailed instruction for this step'),
  time: z.string().optional().describe('Estimated time for this step'),
  temperature: z.string().optional().describe('Temperature if applicable'),
});

const RecipeSchema = z.object({
  name: z.string().describe('Name of the recipe'),
  description: z.string().describe('Brief description of the dish'),
  cuisine: z.string().optional().describe('Type of cuisine (e.g., Italian, Mexican)'),
  difficulty: z.enum(['Easy', 'Medium', 'Hard']).describe('Difficulty level'),
  prepTime: z.string().describe('Preparation time (e.g., "15 minutes")'),
  cookTime: z.string().describe('Cooking time (e.g., "30 minutes")'),
  totalTime: z.string().describe('Total time (e.g., "45 minutes")'),
  servings: z.number().describe('Number of servings'),
  ingredients: z.array(IngredientSchema).describe('List of ingredients'),
  instructions: z.array(InstructionSchema).describe('Step-by-step instructions'),
  tips: z.array(z.string()).optional().describe('Helpful cooking tips'),
  tags: z.array(z.string()).optional().describe('Recipe tags (e.g., vegetarian, gluten-free)'),
});

const NutritionSchema = z.object({
  calories: z.number().describe('Calories per serving'),
  protein: z.string().describe('Protein content (e.g., "25g")'),
  carbs: z.string().describe('Carbohydrate content (e.g., "30g")'),
  fat: z.string().describe('Fat content (e.g., "15g")'),
  fiber: z.string().optional().describe('Fiber content (e.g., "5g")'),
  sugar: z.string().optional().describe('Sugar content (e.g., "10g")'),
});

const SubstitutionSchema = z.object({
  original: z.string().describe('Original ingredient'),
  substitute: z.string().describe('Substitute ingredient'),
  ratio: z.string().describe('Substitution ratio (e.g., "1:1", "2:1")'),
  notes: z.string().optional().describe('Notes about the substitution'),
});

// Recipe generation tools
export const recipeTools = {
  createRecipe: tool({
    description: 'Create and structure a complete recipe with all details',
    parameters: RecipeSchema,
    execute: async (recipe) => {
      return {
        success: true,
        recipe: recipe,
        message: `Created recipe for ${recipe.name}`,
      };
    },
  }),

  suggestIngredientSubstitutions: tool({
    description: 'Suggest ingredient substitutions for a recipe',
    parameters: z.object({
      ingredient: z.string().describe('Ingredient to find substitutions for'),
      reason: z.string().optional().describe('Reason for substitution (allergy, availability, etc.)'),
    }),
    execute: async ({ ingredient, reason }) => {
      // Mock substitution suggestions
      const commonSubstitutions: Record<string, SubstitutionSchema[]> = {
        'butter': [
          { original: 'butter', substitute: 'olive oil', ratio: '3:4', notes: 'Use 3/4 the amount of olive oil' },
          { original: 'butter', substitute: 'coconut oil', ratio: '1:1', notes: 'Works well in baking' },
        ],
        'eggs': [
          { original: 'eggs', substitute: 'flax eggs', ratio: '1:1', notes: '1 tbsp ground flaxseed + 3 tbsp water per egg' },
          { original: 'eggs', substitute: 'applesauce', ratio: '1:1/4 cup', notes: 'Use 1/4 cup applesauce per egg in baking' },
        ],
        'milk': [
          { original: 'milk', substitute: 'almond milk', ratio: '1:1', notes: 'Works in most recipes' },
          { original: 'milk', substitute: 'oat milk', ratio: '1:1', notes: 'Creamy texture, good for baking' },
        ],
      };

      const substitutions = commonSubstitutions[ingredient.toLowerCase()] || [
        { original: ingredient, substitute: 'Similar ingredient', ratio: '1:1', notes: 'Consult cooking resources for specific substitutions' },
      ];

      return {
        success: true,
        substitutions,
        message: `Found ${substitutions.length} substitution(s) for ${ingredient}`,
      };
    },
  }),

  calculateNutrition: tool({
    description: 'Calculate estimated nutritional information for a recipe',
    parameters: z.object({
      recipeName: z.string().describe('Name of the recipe'),
      servings: z.number().describe('Number of servings'),
      mainIngredients: z.array(z.string()).describe('Main ingredients in the recipe'),
    }),
    execute: async ({ recipeName, servings, mainIngredients }) => {
      // Mock nutrition calculation
      const mockNutrition: NutritionSchema = {
        calories: Math.floor(Math.random() * 400) + 200, // 200-600 calories
        protein: `${Math.floor(Math.random() * 30) + 10}g`,
        carbs: `${Math.floor(Math.random() * 50) + 20}g`,
        fat: `${Math.floor(Math.random() * 25) + 5}g`,
        fiber: `${Math.floor(Math.random() * 10) + 2}g`,
        sugar: `${Math.floor(Math.random() * 15) + 5}g`,
      };

      return {
        success: true,
        nutrition: mockNutrition,
        message: `Calculated nutrition information for ${recipeName} (per serving)`,
        disclaimer: 'Nutritional values are estimates and may vary based on specific ingredients and preparation methods.',
      };
    },
  }),

  adjustServingSize: tool({
    description: 'Adjust recipe quantities for a different number of servings',
    parameters: z.object({
      originalServings: z.number().describe('Original number of servings'),
      newServings: z.number().describe('Desired number of servings'),
      recipeName: z.string().describe('Name of the recipe being adjusted'),
    }),
    execute: async ({ originalServings, newServings, recipeName }) => {
      const ratio = newServings / originalServings;

      return {
        success: true,
        ratio,
        message: `Recipe adjusted from ${originalServings} to ${newServings} servings`,
        instructions: [
          `Multiply all ingredient quantities by ${ratio.toFixed(2)}`,
          'Cooking time may need slight adjustment for larger quantities',
          'Use appropriately sized cookware for the new quantity',
        ],
      };
    },
  }),

  suggestSimilarRecipes: tool({
    description: 'Suggest similar recipes based on a given recipe or food type',
    parameters: z.object({
      foodType: z.string().describe('Type of food or recipe name'),
      cuisine: z.string().optional().describe('Preferred cuisine type'),
      difficulty: z.string().optional().describe('Preferred difficulty level'),
    }),
    execute: async ({ foodType, cuisine, difficulty }) => {
      // Mock similar recipe suggestions
      const suggestions = [
        `${foodType} Variation 1`,
        `${cuisine || 'International'} Style ${foodType}`,
        `Easy ${foodType} Recipe`,
        `Gourmet ${foodType}`,
        `Healthy ${foodType} Alternative`,
      ].slice(0, 3);

      return {
        success: true,
        suggestions,
        message: `Found ${suggestions.length} similar recipes to ${foodType}`,
      };
    },
  }),
};
