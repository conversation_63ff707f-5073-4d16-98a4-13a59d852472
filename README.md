# 🍳 AI Recipe Generator

A specialized AI recipe assistant that creates delicious recipes based on food names you provide. This assistant <PERSON><PERSON><PERSON> responds to cooking and recipe-related questions, making it a focused culinary tool. Built with Next.js, AI SDK, and Ollama.

## ✨ Features

### 🤖 AI-Powered Recipe Generation

- **Smart Recipe Creation**: Just enter a food name and get a complete recipe with ingredients, instructions, and cooking tips
- **Focused Assistant**: Only responds to cooking and recipe-related questions - politely declines other topics
- **Professional Chef Guidance**: AI acts as a professional chef providing detailed cooking instructions

### 🛠️ Advanced Recipe Tools

- **`generateRecipe`**: Creates complete recipes with ingredients, instructions, timing, and difficulty levels
- **`suggestIngredientSubstitutions`**: Provides alternative ingredients for dietary restrictions or availability
- **`calculateNutrition`**: Estimates nutritional information per serving
- **`adjustServingSize`**: Scales recipes for different numbers of servings
- **`suggestSimilarRecipes`**: Recommends related recipes based on cuisine and preferences

### 🎨 Beautiful UI

- **Recipe Display Component**: Beautifully formatted recipe cards with ingredients, instructions, and metadata
- **Interactive Chat Interface**: Real-time conversation with the AI chef
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Tool Call Visualization**: See which AI tools are being used in real-time

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm
- [Ollama](https://ollama.ai/) installed and running locally
- Llama 3.2 model downloaded in Ollama

### Installation

1. **Clone the repository**

   ```bash
   git clone <your-repo-url>
   cd my-ai-app
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Start Ollama (if not already running)**

   ```bash
   ollama serve
   ```

4. **Pull the Llama 3.2 model**

   ```bash
   ollama pull llama3.2:latest
   ```

5. **Run the development server**

   ```bash
   pnpm dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) (or the port shown in terminal)

## 🎯 How to Use

1. **Enter a Food Name**: Type any dish name in the input field (e.g., "pasta carbonara", "chocolate cake", "chicken curry")
2. **Generate Recipe**: Click the "Generate Recipe" button or press Enter
3. **View Results**: The AI will create a complete recipe with:
   - Ingredient list with measurements
   - Step-by-step instructions
   - Cooking times and difficulty level
   - Helpful tips and tags
4. **Recipe-Only Focus**: The assistant only responds to cooking and recipe questions - it will politely decline other topics

## 🏗️ Project Structure

```
src/
├── app/
│   ├── api/chat/route.ts      # AI chat API with tool integration
│   ├── layout.tsx             # App layout and metadata
│   └── page.tsx               # Main recipe generator interface
├── components/
│   └── RecipeDisplay.tsx      # Recipe display component
├── lib/
│   └── recipe-tools.ts        # AI tool definitions and implementations
└── types/
    └── recipe.ts              # TypeScript type definitions
```

## 🔧 Technical Details

### AI Tools Implementation

The application uses the AI SDK's tool system with Zod schemas for type safety:

- **Structured Output**: All tools return consistent, typed data
- **Error Handling**: Robust error handling for tool execution
- **Mock Data**: Currently uses mock implementations (easily replaceable with real APIs)

### Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **AI**: AI SDK, Ollama, Llama 3.2
- **Type Safety**: Zod schemas for runtime validation

## 🎨 Customization

### Adding New Tools

1. Define the tool in `src/lib/recipe-tools.ts`
2. Add Zod schema for parameters and return types
3. Implement the execution logic
4. Update the UI to handle new tool results

### Styling

The app uses Tailwind CSS. Customize the design by modifying the classes in:

- `src/app/page.tsx` - Main interface
- `src/components/RecipeDisplay.tsx` - Recipe cards

### AI Model

Change the AI model by updating `src/app/api/chat/route.ts`:

```typescript
const model = ollama("your-preferred-model");
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- [AI SDK](https://sdk.vercel.ai/) for the excellent AI integration tools
- [Ollama](https://ollama.ai/) for local AI model hosting
- [Next.js](https://nextjs.org/) for the amazing React framework
- [Tailwind CSS](https://tailwindcss.com/) for beautiful styling
